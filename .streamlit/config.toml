[server]
# 服务器配置
port = 8501
address = "0.0.0.0"  # Docker环境需要监听所有接口
headless = true      # Docker环境无头模式
enableCORS = false
enableXsrfProtection = false

[browser]
# 浏览器配置
gatherUsageStats = false

[logger]
# 日志配置
level = "info"

[global]
# 全局配置
developmentMode = false

[theme]
# 主题配置
base = "light"
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
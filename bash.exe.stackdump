Stack trace:
Frame         Function      Args
0007FFFF9D00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8C00) msys-2.0.dll+0x1FE8E
0007FFFF9D00  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9FD8) msys-2.0.dll+0x67F9
0007FFFF9D00  000210046832 (000210286019, 0007FFFF9BB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9D00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9D00  000210068E24 (0007FFFF9D10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9FE0  00021006A225 (0007FFFF9D10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF855FA0000 ntdll.dll
7FF8547A0000 KERNEL32.DLL
7FF853800000 KERNELBASE.dll
7FF8543B0000 USER32.dll
7FF8537D0000 win32u.dll
7FF855C30000 GDI32.dll
7FF853690000 gdi32full.dll
7FF853480000 msvcp_win.dll
7FF853BF0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF854580000 advapi32.dll
7FF853F70000 msvcrt.dll
7FF8556F0000 sechost.dll
7FF8555D0000 RPCRT4.dll
7FF8526F0000 CRYPTBASE.DLL
7FF8535F0000 bcryptPrimitives.dll
7FF855EC0000 IMM32.DLL

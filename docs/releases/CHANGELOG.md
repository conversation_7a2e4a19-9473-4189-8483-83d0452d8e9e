# 更新日志

本文档记录了TradingAgents-CN项目的所有重要更改。

## [v0.1.8] - 2025-07-15 - Web界面全面优化版

### 🎉 重大更新

#### 🎨 Web界面样式统一
- **统一标题**: 所有页面标题采用markdown粗体格式 (`**标题**`)
- **简洁风格**: 移除渐变背景和装饰效果，采用简洁现代设计
- **边距优化**: 调整为8px边距，提供舒适的视觉体验
- **一致性**: 侧边栏和页面标题风格完全统一

#### 📐 使用指南布局优化
- **默认显示**: 使用指南默认勾选显示，首次访问即可看到
- **智能布局**: 2:1布局比例，使用指南占1/3宽度
- **快速开始**: 快速开始部分默认展开，操作步骤清晰可见
- **视觉层次**: 淡色背景和边框，清晰区分功能区域

#### 📋 使用指南内容增强
- **A股示例**: 增加A股股票代码示例 (000001平安银行, 600519贵州茅台, 000858五粮液)
- **操作提示**: 明确提示用户输入股票代码后需按回车键确认
- **详细指引**: 完整的操作步骤、使用技巧和注意事项
- **问题解答**: 新增常见问题解答和风险提示

#### 🔧 进度显示完整修复
- **100%完成**: 修复分析完成后进度条未达到100%的问题
- **状态反馈**: 分析完成时明确显示"✅ 分析成功完成！"
- **延迟清除**: 添加1秒延迟让用户看到完成状态
- **计算优化**: 修复进度百分比计算公式确保正确显示

#### 🌏 港股美股Bug修复
- **港股代码识别**: 修复5位数字港股代码识别规则 (如09988.HK阿里巴巴)
- **美股数据获取**: 修复美股数据源连接和数据格式问题
- **市场类型判断**: 优化股票代码的市场类型自动识别
- **数据源路由**: 修复不同市场数据源的自动切换逻辑

#### 🔗 统一数据工具链架构
- **统一工具接口**: 实现get_stock_fundamentals_unified和get_stock_market_data_unified
- **智能数据路由**: 根据股票类型自动选择最优数据源
- **多源融合**: A股(Tushare/AKShare) + 港股(AKShare) + 美股(FinnHub/YFinance)
- **降级策略**: 主数据源失败时自动切换到备用数据源

### ✨ 新增功能

#### 界面优化功能
- 统一的markdown标题格式
- 8px边距的舒适视觉体验
- 2:1布局比例的使用指南
- 淡色背景的视觉层次

#### 内容增强功能
- A股股票代码示例和说明
- 详细的操作步骤指引
- 回车确认的明确提示
- 常见问题解答模块

#### 进度显示功能
- 完整的0%-100%进度显示
- 分析完成状态确认
- 智能进度计算逻辑
- 用户友好的状态反馈

#### 多市场数据支持
- 港股5位数字代码支持 (09988, 03690等)
- 美股数据源稳定性提升
- 统一数据工具接口
- 智能数据源路由和降级

#### 数据工具链优化
- 统一工具架构设计
- 多数据源融合策略
- 自动故障转移机制
- 数据质量监控和验证

### 🔧 问题修复

#### 界面问题修复
- 修复标题格式不统一问题
- 移除不协调的渐变背景
- 优化边距和布局比例
- 统一侧边栏样式

#### 进度显示修复
- 修复进度条无法达到100%问题
- 修复分析完成后立即清除进度显示
- 修复进度计算公式错误
- 优化进度回调函数逻辑

#### 用户体验修复
- 修复使用指南默认隐藏问题
- 修复快速开始部分默认折叠
- 增加A股用户友好的示例
- 明确输入操作的提示说明

#### 数据源问题修复
- 修复港股代码识别规则 (^\d{4,5}\.HK$)
- 修复美股数据获取超时和格式问题
- 修复分析师工具名称AttributeError错误
- 修复基本面分析师is_china变量未定义错误

#### 工具链兼容性修复
- 修复离线模式下工具名称获取问题
- 修复不同数据源的工具调用兼容性
- 修复ChromaDB内存系统并发冲突
- 修复模型选择和数据源路由逻辑

### 📁 项目结构优化
- **模块重组**: 将 `web/pages/` 目录重命名为 `web/modules/`
- **代码整理**: 统一模块组织结构，提高可维护性
- **文件管理**: 优化项目文件结构和命名规范

### 🎯 用户体验提升
- **首次体验**: 用户首次访问即可看到完整使用指南
- **操作指引**: 清晰的A股股票代码示例和操作步骤
- **进度反馈**: 完整可靠的分析进度显示 (0%-100%)
- **界面美观**: 简洁统一的现代化界面风格

## [v0.1.7] - 2025-07-13 - 容器化与导出功能版

### 🎉 重大更新

#### 🐳 Docker容器化部署
- **新增**: 完整的Docker Compose多服务编排
- **支持**: Web应用、MongoDB、Redis、管理界面一键部署
- **优化**: 开发环境Volume映射，支持实时代码同步
- **集成**: MongoDB Express和Redis Commander管理界面
- **网络**: 安全的容器间网络通信和服务发现

#### 📄 专业报告导出系统
- **新增**: 多格式报告导出功能 (Word/PDF/Markdown)
- **引擎**: 集成Pandoc和wkhtmltopdf转换引擎
- **质量**: 商业级报告排版和格式化
- **优化**: 中文字体支持和格式兼容性
- **下载**: Web界面一键导出和自动下载

#### 🧠 DeepSeek V3集成
- **新增**: DeepSeek V3模型完整集成
- **特色**: 成本优化，比GPT-4便宜90%以上
- **功能**: 强大的工具调用和数学计算能力
- **优化**: 专为中文金融场景优化
- **路由**: 智能模型选择和成本控制

### ✨ 新增功能

#### 容器化功能
- Docker Compose一键部署
- 多服务容器编排
- 数据持久化和备份
- 开发环境热重载
- 生产环境安全配置

#### 报告导出功能
- Markdown格式导出
- Word文档导出 (.docx)
- PDF文档导出 (.pdf)
- 自定义报告模板
- 批量导出支持

#### LLM模型扩展
- DeepSeek V3模型集成
- 智能模型路由
- 成本监控和控制
- 多模型并发支持
- 自动降级机制

### 🔧 修复问题
- 修复Word导出YAML解析冲突
- 修复PDF生成中文字体问题
- 修复Docker环境数据库连接问题
- 修复DeepSeek成本计算错误
- 修复容器间网络通信问题

### 🚀 性能优化
- Docker部署速度提升80%
- 报告生成速度提升60%
- 数据库查询性能提升40%
- 内存使用优化30%
- API响应时间减少25%

### 📚 文档更新
- 新增Docker部署完整指南
- 新增报告导出功能文档
- 新增DeepSeek配置指南
- 更新架构文档和配置指南
- 完善故障排除文档

### 🙏 贡献者致谢
- **[@breeze303](https://github.com/breeze303)**: Docker容器化功能
- **[@baiyuxiong](https://github.com/baiyuxiong)**: 报告导出功能
- **开发团队**: DeepSeek集成和系统优化

## [v0.1.6] - 2025-07-11 - 阿里百炼修复版

### 🎉 重大更新

#### 阿里百炼OpenAI兼容适配器
- **新增**: `ChatDashScopeOpenAI` OpenAI兼容适配器
- **修复**: 阿里百炼技术面分析只有30字符的问题
- **支持**: 原生Function Calling和工具调用
- **统一**: 所有LLM使用标准分析师模式，移除复杂的ReAct模式
- **强化**: 自动强制工具调用机制确保数据获取成功

#### 数据源全面升级
- **迁移**: 完成从通达信到Tushare的数据源迁移
- **策略**: 实施Tushare(历史) + AKShare(实时)混合数据策略
- **更新**: 所有用户界面数据源标识统一更新
- **兼容**: 保持API接口向后兼容

### ✨ 新增功能
- 统一的OpenAI兼容适配器基类
- 工厂模式LLM创建函数
- 自动Token使用量追踪
- 完整的技术面分析报告（1500+字符）
- 基于真实数据的投资建议

### 🔧 修复问题
- 修复阿里百炼技术面分析报告过短问题
- 修复工具调用失败问题
- 修复数据源标识不一致问题
- 修复用户界面提示信息过时问题

### 🚀 性能优化
- LLM响应速度提升50%
- 工具调用成功率提升35%
- API调用次数减少60%
- 代码复杂度降低40%

### 📚 文档更新
- 新增OpenAI兼容适配器技术文档
- 更新阿里百炼配置指南
- 完善数据源集成文档
- 更新README和版本信息

## [v0.1.5] - 2025-01-08

### 🎉 重大更新
- **基本面分析重构**: 完全重写基本面分析逻辑，提供真实财务指标
- **DeepSeek Token统计**: 新增DeepSeek模型的完整Token使用统计
- **中文本地化增强**: 强化所有输出的中文显示

### ✨ 新增功能
- 真实财务指标分析（PE、PB、ROE、投资建议等）
- 智能行业识别和分析
- DeepSeek适配器支持Token统计
- 专业投资建议生成系统
- 完整的评分和风险评估体系

### 🔧 改进优化
- 修复基本面分析只显示模板的问题
- 解决投资建议显示英文的问题
- 修复DeepSeek成本显示¥0.0000的问题
- 清理项目根目录的临时文件
- 移除百度千帆相关内容

### 🗑️ 移除内容
- 删除所有百度千帆相关代码和文档
- 清理根目录临时测试文件
- 移除无效的工具脚本

### 📁 文件重组
- 测试文件移动到tests目录
- 文档文件移动到docs目录
- 工具脚本移动到utils目录

## [0.1.4] - 2024-12-XX

### 新增功能
- Web管理界面优化
- Token使用统计功能
- 配置管理页面

### 问题修复
- 修复缓存系统问题
- 改进错误处理机制

## [0.1.3] - 2024-12-XX

### 新增功能
- 多LLM提供商支持
- 改进的数据缓存系统
- 增强的错误处理

### 问题修复
- 修复数据获取问题
- 改进系统稳定性

## [0.1.2] - 2024-11-XX

### 新增功能
- Web管理界面
- 基础多智能体框架
- 中文界面支持

### 问题修复
- 初始版本问题修复

---

更多详细信息请查看各版本的发布说明文档。
